spring.application.name=optical-storage

# MQTT Configuration
mqtt.broker.url=tcp://localhost:1883
mqtt.client.id=optical-storage-client-${random.uuid}
mqtt.username=
mqtt.password=
mqtt.default.topic=report/allpoints
mqtt.default.qos=1
mqtt.connection.timeout=30
mqtt.keep.alive.interval=60
mqtt.completion.timeout=30000

# MQTT Topics Configuration
mqtt.publish.topic=report/allpoints
mqtt.subscribe.topic=testSub
mqtt.subscribe.specific.topic.format=configTopic/{saleid}/{gateid}
mqtt.command.topic=

# MQTT Message Format Configuration
mqtt.message.format=plain
# Options: plain, compress_encrypt, compress_only, encrypt_only
mqtt.message.encrypt.key=1234567890123456
mqtt.message.encrypt.iv=1234567890123456
mqtt.message.enable.compression=false
mqtt.message.enable.encryption=false

# Gateway Configuration
gateway.sale.id=10100001
gateway.gate.id=10100001001

# Async Task Executor Configuration
async.executor.core.pool.size=5
async.executor.max.pool.size=10
async.executor.queue.capacity=25
async.executor.thread.name.prefix=mqtt-async-

# Response Management Configuration
mqtt.response.cleanup.timeout.minutes=30
mqtt.response.default.timeout.seconds=30