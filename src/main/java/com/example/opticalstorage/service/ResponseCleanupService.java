package com.example.opticalstorage.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 响应清理服务，定期清理超时的响应
 */
@Service
public class ResponseCleanupService {

    private static final Logger logger = LoggerFactory.getLogger(ResponseCleanupService.class);

    @Autowired
    private GatewayMessageService gatewayMessageService;

    @Value("${mqtt.response.cleanup.timeout.minutes:30}")
    private long timeoutMinutes;

    /**
     * 定期清理超时的响应
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600,000毫秒
    public void cleanupTimeoutResponses() {
        try {
            logger.debug("Starting cleanup of timeout responses...");
            
            // 获取清理前的统计信息
            var statsBefore = gatewayMessageService.getPendingResponseStats();
            int totalBefore = statsBefore.values().stream().mapToInt(Integer::intValue).sum();
            
            // 执行清理
            gatewayMessageService.cleanupTimeoutResponses(timeoutMinutes);
            
            // 获取清理后的统计信息
            var statsAfter = gatewayMessageService.getPendingResponseStats();
            int totalAfter = statsAfter.values().stream().mapToInt(Integer::intValue).sum();
            
            int cleaned = totalBefore - totalAfter;
            if (cleaned > 0) {
                logger.info("Cleaned up {} timeout responses. Remaining: {}", cleaned, totalAfter);
            } else {
                logger.debug("No timeout responses to clean up. Current pending: {}", totalAfter);
            }
            
        } catch (Exception e) {
            logger.error("Error during response cleanup", e);
        }
    }

    /**
     * 获取当前待处理响应的统计信息
     *
     * @return 统计信息的字符串表示
     */
    public String getPendingResponseStatsString() {
        var stats = gatewayMessageService.getPendingResponseStats();
        StringBuilder sb = new StringBuilder();
        sb.append("Pending responses: ");
        stats.forEach((type, count) -> sb.append(type).append("=").append(count).append(" "));
        return sb.toString().trim();
    }

    /**
     * 手动触发清理超时响应
     *
     * @param timeoutMinutes 超时时间（分钟）
     * @return 清理的响应数量
     */
    public int manualCleanup(long timeoutMinutes) {
        logger.info("Manual cleanup triggered with timeout {} minutes", timeoutMinutes);
        
        var statsBefore = gatewayMessageService.getPendingResponseStats();
        int totalBefore = statsBefore.values().stream().mapToInt(Integer::intValue).sum();
        
        gatewayMessageService.cleanupTimeoutResponses(timeoutMinutes);
        
        var statsAfter = gatewayMessageService.getPendingResponseStats();
        int totalAfter = statsAfter.values().stream().mapToInt(Integer::intValue).sum();
        
        int cleaned = totalBefore - totalAfter;
        logger.info("Manual cleanup completed. Cleaned: {}, Remaining: {}", cleaned, totalAfter);
        
        return cleaned;
    }

    /**
     * 清理所有待处理的响应
     *
     * @return 清理的响应数量
     */
    public int clearAllResponses() {
        logger.warn("Clearing ALL pending responses - this should only be done during shutdown or emergency");
        
        var statsBefore = gatewayMessageService.getPendingResponseStats();
        int totalBefore = statsBefore.values().stream().mapToInt(Integer::intValue).sum();
        
        gatewayMessageService.clearAllPendingResponses();
        
        logger.warn("Cleared {} pending responses", totalBefore);
        return totalBefore;
    }
}
