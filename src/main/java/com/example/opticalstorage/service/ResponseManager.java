package com.example.opticalstorage.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 通用响应管理器，用于管理异步响应的Future对象
 * 支持泛型，可以处理不同类型的响应消息
 *
 * @param <T> 响应消息的类型
 */
@Component
public class ResponseManager<T> {

    private static final Logger logger = LoggerFactory.getLogger(ResponseManager.class);

    /**
     * 存储响应Future的Map，key为请求ID，value为对应的Future
     */
    private final Map<String, ResponseContext<T>> responses = new ConcurrentHashMap<>();

    /**
     * 默认超时时间（秒）
     */
    private static final long DEFAULT_TIMEOUT_SECONDS = 30;

    /**
     * 响应上下文，包含Future和创建时间
     */
    private static class ResponseContext<T> {
        private final CompletableFuture<T> future;
        private final LocalDateTime createdTime;
        private final String requestType;

        public ResponseContext(CompletableFuture<T> future, String requestType) {
            this.future = future;
            this.createdTime = LocalDateTime.now();
            this.requestType = requestType;
        }

        public CompletableFuture<T> getFuture() {
            return future;
        }

        public LocalDateTime getCreatedTime() {
            return createdTime;
        }

        public String getRequestType() {
            return requestType;
        }
    }

    /**
     * 创建一个新的响应Future
     *
     * @param requestId   请求的唯一标识符
     * @param requestType 请求类型，用于日志记录
     * @return 新创建的CompletableFuture
     */
    public CompletableFuture<T> createResponse(String requestId, String requestType) {
        CompletableFuture<T> future = new CompletableFuture<>();
        ResponseContext<T> context = new ResponseContext<>(future, requestType);
        
        responses.put(requestId, context);
        
        // 设置超时处理
        future.orTimeout(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.warn("Response timeout or error for request {} ({}): {}", 
                                requestId, requestType, throwable.getMessage());
                        responses.remove(requestId);
                    }
                });

        logger.debug("Created response future for request {} ({})", requestId, requestType);
        return future;
    }

    /**
     * 创建一个新的响应Future，使用自定义超时时间
     *
     * @param requestId      请求的唯一标识符
     * @param requestType    请求类型，用于日志记录
     * @param timeoutSeconds 超时时间（秒）
     * @return 新创建的CompletableFuture
     */
    public CompletableFuture<T> createResponse(String requestId, String requestType, long timeoutSeconds) {
        CompletableFuture<T> future = new CompletableFuture<>();
        ResponseContext<T> context = new ResponseContext<>(future, requestType);
        
        responses.put(requestId, context);
        
        // 设置自定义超时处理
        future.orTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.warn("Response timeout or error for request {} ({}): {}", 
                                requestId, requestType, throwable.getMessage());
                        responses.remove(requestId);
                    }
                });

        logger.debug("Created response future for request {} ({}) with timeout {}s", 
                requestId, requestType, timeoutSeconds);
        return future;
    }

    /**
     * 完成指定请求的响应
     *
     * @param requestId 请求的唯一标识符
     * @param response  响应对象
     * @return 如果成功完成响应返回true，如果请求不存在返回false
     */
    public boolean completeResponse(String requestId, T response) {
        ResponseContext<T> context = responses.remove(requestId);
        if (context != null) {
            context.getFuture().complete(response);
            logger.debug("Completed response for request {} ({})", requestId, context.getRequestType());
            return true;
        } else {
            logger.warn("No pending response found for request {}", requestId);
            return false;
        }
    }

    /**
     * 以异常方式完成指定请求的响应
     *
     * @param requestId 请求的唯一标识符
     * @param throwable 异常对象
     * @return 如果成功完成响应返回true，如果请求不存在返回false
     */
    public boolean completeExceptionally(String requestId, Throwable throwable) {
        ResponseContext<T> context = responses.remove(requestId);
        if (context != null) {
            context.getFuture().completeExceptionally(throwable);
            logger.debug("Completed response exceptionally for request {} ({}): {}", 
                    requestId, context.getRequestType(), throwable.getMessage());
            return true;
        } else {
            logger.warn("No pending response found for request {}", requestId);
            return false;
        }
    }

    /**
     * 取消指定请求的响应
     *
     * @param requestId 请求的唯一标识符
     * @return 如果成功取消返回true，如果请求不存在返回false
     */
    public boolean cancelResponse(String requestId) {
        ResponseContext<T> context = responses.remove(requestId);
        if (context != null) {
            context.getFuture().cancel(true);
            logger.debug("Cancelled response for request {} ({})", requestId, context.getRequestType());
            return true;
        } else {
            logger.warn("No pending response found for request {}", requestId);
            return false;
        }
    }

    /**
     * 检查指定请求是否存在待处理的响应
     *
     * @param requestId 请求的唯一标识符
     * @return 如果存在待处理的响应返回true，否则返回false
     */
    public boolean hasPendingResponse(String requestId) {
        return responses.containsKey(requestId);
    }

    /**
     * 获取当前待处理响应的数量
     *
     * @return 待处理响应的数量
     */
    public int getPendingResponseCount() {
        return responses.size();
    }

    /**
     * 清理所有待处理的响应（通常在关闭时调用）
     */
    public void clearAllResponses() {
        logger.info("Clearing {} pending responses", responses.size());
        responses.values().forEach(context -> 
                context.getFuture().cancel(true));
        responses.clear();
    }

    /**
     * 清理超时的响应（可以定期调用）
     *
     * @param timeoutMinutes 超时时间（分钟）
     */
    public void cleanupTimeoutResponses(long timeoutMinutes) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        responses.entrySet().removeIf(entry -> {
            ResponseContext<T> context = entry.getValue();
            if (context.getCreatedTime().isBefore(cutoffTime)) {
                context.getFuture().cancel(true);
                logger.debug("Cleaned up timeout response for request {} ({})", 
                        entry.getKey(), context.getRequestType());
                return true;
            }
            return false;
        });
    }
}
