package com.example.opticalstorage.service;

import com.example.opticalstorage.model.mqtt.*;
import com.example.opticalstorage.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 服务器命令服务，负责服务器主动发送各种命令到网关
 * 包括：召读历史数据、控制命令、遥调命令、获取当前数据、多值写入、储能策略等
 */
@Service
public class ServerCommandService {

    private static final Logger logger = LoggerFactory.getLogger(ServerCommandService.class);

    private final MqttPublisher mqttPublisher;
    private final MessageFormatService messageFormatService;
    private final GatewayMessageService gatewayMessageService;

    @Value("${gateway.sale.id}")
    private String saleId;

    @Value("${gateway.gate.id}")
    private String gateId;

    @Value("${mqtt.command.topic:#{null}}")
    private String commandTopic;

    public ServerCommandService(MqttPublisher mqttPublisher, 
                               MessageFormatService messageFormatService,
                               GatewayMessageService gatewayMessageService) {
        this.mqttPublisher = mqttPublisher;
        this.messageFormatService = messageFormatService;
        this.gatewayMessageService = gatewayMessageService;
    }

    /**
     * 发送召读历史数据命令
     *
     * @param targetSaleId 目标变电所编号
     * @param targetGateId 目标网关编号
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 是否发送成功
     */
    public boolean sendCallReadCommand(String targetSaleId, String targetGateId, 
                                     String startTime, String endTime) {
        CallReadMessage message = new CallReadMessage(
                targetSaleId,
                targetGateId,
                MessageUtils.getCurrentTime(),
                startTime,
                endTime
        );

        return publishCommandMessage(targetSaleId, targetGateId, message);
    }

    /**
     * 发送控制命令
     *
     * @param targetSaleId 目标变电所编号
     * @param targetGateId 目标网关编号
     * @param meterId      仪表编号
     * @param name         仪表名称
     * @param functionId   功能点位
     * @param value        控制值
     * @return 控制响应的Future
     */
    public CompletableFuture<ControlAckMessage> sendControlCommand(String targetSaleId, String targetGateId,
                                                                  String meterId, String name, 
                                                                  String functionId, Integer value) {
        String cuuid = generateCuuid();
        
        ControlMessage message = new ControlMessage(
                targetSaleId,
                targetGateId,
                MessageUtils.getCurrentTime(),
                cuuid,
                meterId,
                name,
                functionId,
                value
        );

        // 创建响应Future
        CompletableFuture<ControlAckMessage> responseFuture = 
                gatewayMessageService.createControlResponse(cuuid);

        // 发送命令
        boolean sent = publishCommandMessage(targetSaleId, targetGateId, message);
        if (!sent) {
            responseFuture.completeExceptionally(new RuntimeException("Failed to send control command"));
        }

        return responseFuture;
    }

    /**
     * 发送遥调命令
     *
     * @param targetSaleId 目标变电所编号
     * @param targetGateId 目标网关编号
     * @param meterId      仪表编号
     * @param name         仪表名称
     * @param functionId   功能点位
     * @param value        设置值
     * @return 遥调响应的Future
     */
    public CompletableFuture<SetAckMessage> sendSetCommand(String targetSaleId, String targetGateId,
                                                          String meterId, String name, 
                                                          String functionId, String value) {
        String cuuid = generateCuuid();
        
        SetMessage message = new SetMessage(
                targetSaleId,
                targetGateId,
                MessageUtils.getCurrentTime(),
                cuuid,
                meterId,
                name,
                functionId,
                value
        );

        // 创建响应Future
        CompletableFuture<SetAckMessage> responseFuture = 
                gatewayMessageService.createSetResponse(cuuid);

        // 发送命令
        boolean sent = publishCommandMessage(targetSaleId, targetGateId, message);
        if (!sent) {
            responseFuture.completeExceptionally(new RuntimeException("Failed to send set command"));
        }

        return responseFuture;
    }

    /**
     * 发送获取当前数据命令
     *
     * @param targetSaleId 目标变电所编号
     * @param targetGateId 目标网关编号
     * @return 是否发送成功
     */
    public boolean sendGetCurrentDataCommand(String targetSaleId, String targetGateId) {
        GetCurrentDataMessage message = new GetCurrentDataMessage(
                targetSaleId,
                targetGateId,
                MessageUtils.getCurrentTime()
        );

        return publishCommandMessage(targetSaleId, targetGateId, message);
    }

    /**
     * 发送多值写入命令
     *
     * @param targetSaleId 目标变电所编号
     * @param targetGateId 目标网关编号
     * @param meterId      仪表编号
     * @param name         仪表名称
     * @param functionId   功能点位
     * @param values       多值数据
     * @return 多值写入响应的Future
     */
    public CompletableFuture<MultValueSetAckMessage> sendMultValueSetCommand(String targetSaleId, String targetGateId,
                                                                            String meterId, String name, 
                                                                            String functionId, Map<String, String> values) {
        String cuuid = generateCuuid();
        
        MultValueSetMessage message = new MultValueSetMessage(
                targetSaleId,
                targetGateId,
                MessageUtils.getCurrentTime(),
                cuuid,
                meterId,
                name,
                functionId,
                values
        );

        // 创建响应Future
        CompletableFuture<MultValueSetAckMessage> responseFuture = 
                gatewayMessageService.createMultValueSetResponse(cuuid);

        // 发送命令
        boolean sent = publishCommandMessage(targetSaleId, targetGateId, message);
        if (!sent) {
            responseFuture.completeExceptionally(new RuntimeException("Failed to send multValueSet command"));
        }

        return responseFuture;
    }

    /**
     * 发送储能策略命令
     *
     * @param targetSaleId  目标变电所编号
     * @param targetGateId  目标网关编号
     * @param taskId        任务ID
     * @param enable        是否启用
     * @param startTime     开始时间
     * @param dateInterval  日期间隔
     * @param bitmapDays    位图天数
     * @param strategyList  策略列表
     * @return 储能策略响应的Future
     */
    public CompletableFuture<StorageStrategyAckMessage> sendStorageStrategyCommand(
            String targetSaleId, String targetGateId, String taskId, boolean enable,
            String startTime, int dateInterval, String bitmapDays, 
            List<StorageStrategyMessage.StrategyData> strategyList) {
        
        String cuuid = generateCuuid();
        
        StorageStrategyMessage message = new StorageStrategyMessage(
                targetSaleId,
                targetGateId,
                MessageUtils.getCurrentTime(),
                taskId,
                cuuid,
                enable,
                startTime,
                dateInterval,
                bitmapDays,
                strategyList
        );

        // 创建响应Future
        CompletableFuture<StorageStrategyAckMessage> responseFuture = 
                gatewayMessageService.createStorageStrategyResponse(cuuid);

        // 发送命令
        boolean sent = publishCommandMessage(targetSaleId, targetGateId, message);
        if (!sent) {
            responseFuture.completeExceptionally(new RuntimeException("Failed to send storage strategy command"));
        }

        return responseFuture;
    }

    /**
     * 发送心跳校时响应（服务器响应网关的心跳请求）
     *
     * @param targetSaleId 目标变电所编号
     * @param targetGateId 目标网关编号
     * @return 是否发送成功
     */
    public boolean sendHeartBeatResponse(String targetSaleId, String targetGateId) {
        HeartBeatMessage message = new HeartBeatMessage(
                targetSaleId,
                targetGateId,
                MessageUtils.getCurrentTime(),
                "time"
        );

        return publishCommandMessage(targetSaleId, targetGateId, message);
    }

    /**
     * 发布命令消息到指定主题
     *
     * @param targetSaleId 目标变电所编号
     * @param targetGateId 目标网关编号
     * @param message      消息对象
     * @return 是否发送成功
     */
    private boolean publishCommandMessage(String targetSaleId, String targetGateId, Object message) {
        try {
            String json = MessageUtils.toJson(message);
            String formattedMessage = messageFormatService.formatOutboundMessage(json);
            
            // 构建目标主题：配置主题/${saleid}/${gateid}
            String targetTopic = buildTargetTopic(targetSaleId, targetGateId);
            
            boolean sent = mqttPublisher.publish(targetTopic, formattedMessage);
            
            if (sent) {
                logger.info("Successfully sent command to topic: {}, message type: {}", 
                           targetTopic, message.getClass().getSimpleName());
            } else {
                logger.error("Failed to send command to topic: {}, message type: {}", 
                            targetTopic, message.getClass().getSimpleName());
            }
            
            return sent;
        } catch (Exception e) {
            logger.error("Error sending command message", e);
            return false;
        }
    }

    /**
     * 构建目标主题
     *
     * @param targetSaleId 目标变电所编号
     * @param targetGateId 目标网关编号
     * @return 目标主题
     */
    private String buildTargetTopic(String targetSaleId, String targetGateId) {
        if (commandTopic != null && !commandTopic.isEmpty()) {
            return commandTopic;
        }
        // 根据API文档：配置主题/${saleid}/${gateid}
        return String.format("config/%s/%s", targetSaleId, targetGateId);
    }

    /**
     * 生成控制命令唯一编号
     *
     * @return 唯一编号
     */
    private String generateCuuid() {
        return System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8);
    }
}
