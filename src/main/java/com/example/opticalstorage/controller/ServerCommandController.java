package com.example.opticalstorage.controller;

import com.example.opticalstorage.model.mqtt.*;
import com.example.opticalstorage.service.ServerCommandService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 服务器命令控制器，提供服务器向网关发送各种命令的API
 */
@RestController
@RequestMapping("/api/server")
public class ServerCommandController {
    
    private static final Logger logger = LoggerFactory.getLogger(ServerCommandController.class);
    
    private final ServerCommandService serverCommandService;
    
    public ServerCommandController(ServerCommandService serverCommandService) {
        this.serverCommandService = serverCommandService;
    }
    
    /**
     * 发送召读历史数据命令
     */
    @PostMapping("/call-read")
    public ResponseEntity<Map<String, Object>> sendCallRead(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String startTime,
            @RequestParam String endTime) {
        
        boolean sent = serverCommandService.sendCallReadCommand(targetSaleId, targetGateId, startTime, endTime);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", sent);
        response.put("message", "Call read command sent");
        response.put("targetSaleId", targetSaleId);
        response.put("targetGateId", targetGateId);
        response.put("startTime", startTime);
        response.put("endTime", endTime);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送控制命令
     */
    @PostMapping("/control")
    public ResponseEntity<Map<String, Object>> sendControl(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestParam Integer value) {
        
        try {
            CompletableFuture<ControlAckMessage> future = serverCommandService.sendControlCommand(
                    targetSaleId, targetGateId, meterId, name, functionId, value);
            
            // 等待响应（最多30秒）
            ControlAckMessage ackMessage = future.get(30, TimeUnit.SECONDS);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Control command sent and acknowledged");
            response.put("ackMessage", ackMessage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error sending control command", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Control command failed: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 发送控制命令（异步，不等待响应）
     */
    @PostMapping("/control/async")
    public ResponseEntity<Map<String, Object>> sendControlAsync(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestParam Integer value) {
        
        CompletableFuture<ControlAckMessage> future = serverCommandService.sendControlCommand(
                targetSaleId, targetGateId, meterId, name, functionId, value);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Control command sent asynchronously");
        response.put("targetSaleId", targetSaleId);
        response.put("targetGateId", targetGateId);
        response.put("meterId", meterId);
        response.put("functionId", functionId);
        response.put("value", value);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送遥调命令
     */
    @PostMapping("/set")
    public ResponseEntity<Map<String, Object>> sendSet(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestParam String value) {
        
        try {
            CompletableFuture<SetAckMessage> future = serverCommandService.sendSetCommand(
                    targetSaleId, targetGateId, meterId, name, functionId, value);
            
            // 等待响应（最多30秒）
            SetAckMessage ackMessage = future.get(30, TimeUnit.SECONDS);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Set command sent and acknowledged");
            response.put("ackMessage", ackMessage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error sending set command", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Set command failed: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 发送遥调命令（异步，不等待响应）
     */
    @PostMapping("/set/async")
    public ResponseEntity<Map<String, Object>> sendSetAsync(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestParam String value) {
        
        CompletableFuture<SetAckMessage> future = serverCommandService.sendSetCommand(
                targetSaleId, targetGateId, meterId, name, functionId, value);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Set command sent asynchronously");
        response.put("targetSaleId", targetSaleId);
        response.put("targetGateId", targetGateId);
        response.put("meterId", meterId);
        response.put("functionId", functionId);
        response.put("value", value);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送获取当前数据命令
     */
    @PostMapping("/get-current-data")
    public ResponseEntity<Map<String, Object>> sendGetCurrentData(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId) {
        
        boolean sent = serverCommandService.sendGetCurrentDataCommand(targetSaleId, targetGateId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", sent);
        response.put("message", "Get current data command sent");
        response.put("targetSaleId", targetSaleId);
        response.put("targetGateId", targetGateId);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送多值写入命令
     */
    @PostMapping("/mult-value-set")
    public ResponseEntity<Map<String, Object>> sendMultValueSet(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestBody Map<String, String> values) {
        
        try {
            CompletableFuture<MultValueSetAckMessage> future = serverCommandService.sendMultValueSetCommand(
                    targetSaleId, targetGateId, meterId, name, functionId, values);
            
            // 等待响应（最多30秒）
            MultValueSetAckMessage ackMessage = future.get(30, TimeUnit.SECONDS);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Multi value set command sent and acknowledged");
            response.put("ackMessage", ackMessage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error sending multi value set command", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Multi value set command failed: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 发送多值写入命令（异步，不等待响应）
     */
    @PostMapping("/mult-value-set/async")
    public ResponseEntity<Map<String, Object>> sendMultValueSetAsync(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestBody Map<String, String> values) {
        
        CompletableFuture<MultValueSetAckMessage> future = serverCommandService.sendMultValueSetCommand(
                targetSaleId, targetGateId, meterId, name, functionId, values);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Multi value set command sent asynchronously");
        response.put("targetSaleId", targetSaleId);
        response.put("targetGateId", targetGateId);
        response.put("meterId", meterId);
        response.put("functionId", functionId);
        response.put("values", values);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送储能策略命令
     */
    @PostMapping("/storage-strategy")
    public ResponseEntity<Map<String, Object>> sendStorageStrategy(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String taskId,
            @RequestParam(defaultValue = "true") boolean enable,
            @RequestParam String startTime,
            @RequestParam(defaultValue = "30") int dateInterval,
            @RequestParam(defaultValue = "/////w==") String bitmapDays,
            @RequestBody List<StorageStrategyMessage.StrategyData> strategyList) {
        
        try {
            CompletableFuture<StorageStrategyAckMessage> future = serverCommandService.sendStorageStrategyCommand(
                    targetSaleId, targetGateId, taskId, enable, startTime, dateInterval, bitmapDays, strategyList);
            
            // 等待响应（最多30秒）
            StorageStrategyAckMessage ackMessage = future.get(30, TimeUnit.SECONDS);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Storage strategy command sent and acknowledged");
            response.put("ackMessage", ackMessage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error sending storage strategy command", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Storage strategy command failed: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 发送储能策略命令（异步，不等待响应）
     */
    @PostMapping("/storage-strategy/async")
    public ResponseEntity<Map<String, Object>> sendStorageStrategyAsync(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId,
            @RequestParam String taskId,
            @RequestParam(defaultValue = "true") boolean enable,
            @RequestParam String startTime,
            @RequestParam(defaultValue = "30") int dateInterval,
            @RequestParam(defaultValue = "/////w==") String bitmapDays,
            @RequestBody List<StorageStrategyMessage.StrategyData> strategyList) {
        
        CompletableFuture<StorageStrategyAckMessage> future = serverCommandService.sendStorageStrategyCommand(
                targetSaleId, targetGateId, taskId, enable, startTime, dateInterval, bitmapDays, strategyList);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Storage strategy command sent asynchronously");
        response.put("targetSaleId", targetSaleId);
        response.put("targetGateId", targetGateId);
        response.put("taskId", taskId);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送心跳校时响应
     */
    @PostMapping("/heartbeat-response")
    public ResponseEntity<Map<String, Object>> sendHeartBeatResponse(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId) {
        
        boolean sent = serverCommandService.sendHeartBeatResponse(targetSaleId, targetGateId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", sent);
        response.put("message", "Heartbeat response sent");
        response.put("targetSaleId", targetSaleId);
        response.put("targetGateId", targetGateId);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 创建示例储能策略并发送
     */
    @PostMapping("/storage-strategy/sample")
    public ResponseEntity<Map<String, Object>> sendSampleStorageStrategy(
            @RequestParam String targetSaleId,
            @RequestParam String targetGateId) {
        
        // 创建示例策略任务
        Map<String, Object> actionOrders1 = new HashMap<>();
        actionOrders1.put("ChargeP", 10);
        
        StorageStrategyMessage.StorageStrategyTask task1 = new StorageStrategyMessage.StorageStrategyTask(
                "00:00",
                "12:00",
                actionOrders1
        );
        
        Map<String, Object> actionOrders2 = new HashMap<>();
        actionOrders2.put("DisChargeP", 10);
        
        StorageStrategyMessage.StorageStrategyTask task2 = new StorageStrategyMessage.StorageStrategyTask(
                "12:00",
                "24:00",
                actionOrders2
        );
        
        List<StorageStrategyMessage.StorageStrategyTask> tasks = new ArrayList<>();
        tasks.add(task1);
        tasks.add(task2);
        
        StorageStrategyMessage.StrategyData strategyData = new StorageStrategyMessage.StrategyData(
                1, // 计划曲线
                tasks
        );
        
        List<StorageStrategyMessage.StrategyData> strategyList = new ArrayList<>();
        strategyList.add(strategyData);
        
        String taskId = "sample_task_" + System.currentTimeMillis();
        String startTime = String.valueOf(System.currentTimeMillis() / 1000);
        
        try {
            CompletableFuture<StorageStrategyAckMessage> future = serverCommandService.sendStorageStrategyCommand(
                    targetSaleId, targetGateId, taskId, true, startTime, 32, "/////w==", strategyList);
            
            // 等待响应（最多30秒）
            StorageStrategyAckMessage ackMessage = future.get(30, TimeUnit.SECONDS);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Sample storage strategy sent and acknowledged");
            response.put("taskId", taskId);
            response.put("ackMessage", ackMessage);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error sending sample storage strategy", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Sample storage strategy failed: " + e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }
}
