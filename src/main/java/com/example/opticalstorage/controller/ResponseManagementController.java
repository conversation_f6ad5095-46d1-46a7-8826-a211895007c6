package com.example.opticalstorage.controller;

import com.example.opticalstorage.service.ResponseCleanupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 响应管理控制器，提供响应管理相关的监控和管理接口
 */
@RestController
@RequestMapping("/api/response-management")
public class ResponseManagementController {

    @Autowired
    private ResponseCleanupService responseCleanupService;

    /**
     * 获取当前待处理响应的统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getResponseStats() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("stats", responseCleanupService.getPendingResponseStatsString());
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 手动触发清理超时响应
     *
     * @param timeoutMinutes 超时时间（分钟），默认30分钟
     * @return 清理结果
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> manualCleanup(
            @RequestParam(defaultValue = "30") long timeoutMinutes) {
        
        int cleaned = responseCleanupService.manualCleanup(timeoutMinutes);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("cleaned", cleaned);
        response.put("timeoutMinutes", timeoutMinutes);
        response.put("message", "Manual cleanup completed");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 清理所有待处理的响应（危险操作）
     *
     * @param confirm 确认参数，必须为"yes"才能执行
     * @return 清理结果
     */
    @PostMapping("/clear-all")
    public ResponseEntity<Map<String, Object>> clearAllResponses(
            @RequestParam String confirm) {
        
        Map<String, Object> response = new HashMap<>();
        
        if (!"yes".equals(confirm)) {
            response.put("success", false);
            response.put("message", "Confirmation required. Use confirm=yes parameter");
            return ResponseEntity.badRequest().body(response);
        }
        
        int cleared = responseCleanupService.clearAllResponses();
        
        response.put("success", true);
        response.put("cleared", cleared);
        response.put("message", "All pending responses cleared");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 健康检查接口
     *
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Response Management");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
