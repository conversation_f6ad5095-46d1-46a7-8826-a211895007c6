package com.example.opticalstorage.controller;

import com.example.opticalstorage.model.mqtt.*;
import com.example.opticalstorage.service.GatewayMessageService;
import com.example.opticalstorage.service.MqttPublisher;
import com.example.opticalstorage.util.MessageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 网关控制器，提供网关主动发送消息的API
 * 包括：心跳、实时数据上报、报警消息等网关应该主动发送的消息
 */
@RestController
@RequestMapping("/api/gateway")
public class GatewayController {
    
    private static final Logger logger = LoggerFactory.getLogger(GatewayController.class);
    
    private final MqttPublisher mqttPublisher;
    private final GatewayMessageService gatewayMessageService;
    
    @Value("${gateway.sale.id}")
    private String saleId;
    
    @Value("${gateway.gate.id}")
    private String gateId;
    
    @Value("${mqtt.publish.topic}")
    private String publishTopic;
    
    public GatewayController(MqttPublisher mqttPublisher, GatewayMessageService gatewayMessageService) {
        this.mqttPublisher = mqttPublisher;
        this.gatewayMessageService = gatewayMessageService;
    }
    
    /**
     * 发送心跳消息
     */
    @PostMapping("/heartbeat")
    public ResponseEntity<Map<String, Object>> sendHeartbeat() {
        HeartBeatMessage message = new HeartBeatMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                "notify"
        );
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送实时数据
     */
    @PostMapping("/report")
    public ResponseEntity<Map<String, Object>> sendReport() {
        // 创建一个示例报告消息
        ReportMessage message = createSampleReportMessage();
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送报警消息
     */
    @PostMapping("/alarm")
    public ResponseEntity<Map<String, Object>> sendAlarm() {
        // 创建一个示例报警消息
        AlarmMessage message = createSampleAlarmMessage();
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送控制命令
     */
    @PostMapping("/control")
    public ResponseEntity<Map<String, Object>> sendControl(
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestParam Integer value) {
        
        String cuuid = generateCuuid();
        
        ControlMessage message = new ControlMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                cuuid,
                meterId,
                name,
                functionId,
                value
        );
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送遥调命令
     */
    @PostMapping("/set")
    public ResponseEntity<Map<String, Object>> sendSet(
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestParam String value) {
        
        String cuuid = generateCuuid();
        
        SetMessage message = new SetMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                cuuid,
                meterId,
                name,
                functionId,
                value
        );
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送召读历史数据命令
     */
    @PostMapping("/call-read")
    public ResponseEntity<Map<String, Object>> sendCallRead(
            @RequestParam String startTime,
            @RequestParam String endTime) {
        
        CallReadMessage message = new CallReadMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                startTime,
                endTime
        );
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送获取当前数据命令
     */
    @PostMapping("/get-current-data")
    public ResponseEntity<Map<String, Object>> sendGetCurrentData() {
        GetCurrentDataMessage message = new GetCurrentDataMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime()
        );
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送多值写入命令
     */
    @PostMapping("/mult-value-set")
    public ResponseEntity<Map<String, Object>> sendMultValueSet(
            @RequestParam String meterId,
            @RequestParam String name,
            @RequestParam String functionId,
            @RequestBody Map<String, String> values) {
        
        String cuuid = generateCuuid();
        
        MultValueSetMessage message = new MultValueSetMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                cuuid,
                meterId,
                name,
                functionId,
                values
        );
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        response.put("cuuid", cuuid);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 发送储能策略参数下发命令
     */
    @PostMapping("/storage-strategy")
    public ResponseEntity<Map<String, Object>> sendStorageStrategy(
            @RequestBody StorageStrategyMessage.StrategyData strategyData) {
        
        String cuuid = generateCuuid();
        String taskId = "task_" + System.currentTimeMillis();
        
        List<StorageStrategyMessage.StrategyData> strategyList = new ArrayList<>();
        strategyList.add(strategyData);
        
        StorageStrategyMessage message = new StorageStrategyMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                taskId,
                cuuid,
                true, // 默认启用
                String.valueOf(System.currentTimeMillis() / 1000), // 时间戳
                30, // 默认30天
                "/////w==", // 示例bitmap
                strategyList
        );
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        response.put("cuuid", cuuid);
        response.put("taskId", taskId);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 创建示例储能策略
     */
    @PostMapping("/storage-strategy/sample")
    public ResponseEntity<Map<String, Object>> sendSampleStorageStrategy() {
        String cuuid = generateCuuid();
        String taskId = "task_" + System.currentTimeMillis();
        
        // 创建示例策略任务
        Map<String, Object> actionOrders1 = new HashMap<>();
        actionOrders1.put("ChargeP", 10);
        
        StorageStrategyMessage.StorageStrategyTask task1 = new StorageStrategyMessage.StorageStrategyTask(
                "00:00",
                "12:00",
                actionOrders1
        );
        
        Map<String, Object> actionOrders2 = new HashMap<>();
        actionOrders2.put("DisChargeP", 10);
        
        StorageStrategyMessage.StorageStrategyTask task2 = new StorageStrategyMessage.StorageStrategyTask(
                "12:00",
                "24:00",
                actionOrders2
        );
        
        List<StorageStrategyMessage.StorageStrategyTask> tasks = new ArrayList<>();
        tasks.add(task1);
        tasks.add(task2);
        
        StorageStrategyMessage.StrategyData strategyData = new StorageStrategyMessage.StrategyData(
                1, // 计划曲线
                tasks
        );
        
        List<StorageStrategyMessage.StrategyData> strategyList = new ArrayList<>();
        strategyList.add(strategyData);
        
        StorageStrategyMessage message = new StorageStrategyMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                taskId,
                cuuid,
                true,
                String.valueOf(System.currentTimeMillis() / 1000),
                32,
                "/////w==",
                strategyList
        );
        
        String json = MessageUtils.toJson(message);
        boolean published = mqttPublisher.publish(publishTopic, json);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", published);
        response.put("message", message);
        response.put("cuuid", cuuid);
        response.put("taskId", taskId);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 生成控制命令唯一编号
     */
    private String generateCuuid() {
        return System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * 创建示例报告消息
     */
    private ReportMessage createSampleReportMessage() {
        // 使用GatewayMessageService中的方法创建示例报告消息
        return new ReportMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                "100",
                "da"
        );
    }
    
    /**
     * 创建示例报警消息
     */
    private AlarmMessage createSampleAlarmMessage() {
        AlarmMessage message = new AlarmMessage(
                saleId,
                gateId,
                MessageUtils.getCurrentTime(),
                "1",
                "T101001",
                "仪表 AAA"
        );
        
        AlarmMessage.AlarmFunction function1 = new AlarmMessage.AlarmFunction(
                "Ua",
                "A 相电压",
                "高限报警",
                "245.988",
                "1",
                "242"
        );
        
        AlarmMessage.AlarmFunction function2 = new AlarmMessage.AlarmFunction(
                "Ub",
                "B 相电压",
                "报警恢复",
                "241.009",
                "1",
                "198.000-242.000"
        );
        
        message.setFunction(java.util.Arrays.asList(function1, function2));
        
        return message;
    }
}
