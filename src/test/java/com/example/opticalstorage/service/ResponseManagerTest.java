package com.example.opticalstorage.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ResponseManager的单元测试
 */
class ResponseManagerTest {

    private ResponseManager<String> responseManager;

    @BeforeEach
    void setUp() {
        responseManager = new ResponseManager<>();
    }

    @Test
    void testCreateAndCompleteResponse() {
        // 创建响应
        String requestId = "test-001";
        CompletableFuture<String> future = responseManager.createResponse(requestId, "test");

        assertNotNull(future);
        assertFalse(future.isDone());
        assertTrue(responseManager.hasPendingResponse(requestId));
        assertEquals(1, responseManager.getPendingResponseCount());

        // 完成响应
        String responseData = "test response";
        boolean completed = responseManager.completeResponse(requestId, responseData);

        assertTrue(completed);
        assertTrue(future.isDone());
        assertFalse(future.isCompletedExceptionally());
        assertEquals(responseData, future.join());
        assertFalse(responseManager.hasPendingResponse(requestId));
        assertEquals(0, responseManager.getPendingResponseCount());
    }

    @Test
    void testCompleteNonExistentResponse() {
        // 尝试完成不存在的响应
        boolean completed = responseManager.completeResponse("non-existent", "data");
        assertFalse(completed);
    }

    @Test
    void testCompleteExceptionally() {
        // 创建响应
        String requestId = "test-002";
        CompletableFuture<String> future = responseManager.createResponse(requestId, "test");

        // 异常完成
        RuntimeException exception = new RuntimeException("Test exception");
        boolean completed = responseManager.completeExceptionally(requestId, exception);

        assertTrue(completed);
        assertTrue(future.isDone());
        assertTrue(future.isCompletedExceptionally());
        assertFalse(responseManager.hasPendingResponse(requestId));

        // 验证异常
        assertThrows(RuntimeException.class, future::join);
    }

    @Test
    void testCancelResponse() {
        // 创建响应
        String requestId = "test-003";
        CompletableFuture<String> future = responseManager.createResponse(requestId, "test");

        // 取消响应
        boolean cancelled = responseManager.cancelResponse(requestId);

        assertTrue(cancelled);
        assertTrue(future.isDone());
        assertTrue(future.isCancelled());
        assertFalse(responseManager.hasPendingResponse(requestId));
    }

    @Test
    void testTimeout() throws InterruptedException {
        // 创建一个短超时的响应
        String requestId = "test-004";
        CompletableFuture<String> future = responseManager.createResponse(requestId, "test", 1); // 1秒超时

        // 等待超时
        Thread.sleep(1500);

        assertTrue(future.isDone());
        assertTrue(future.isCompletedExceptionally());
        assertFalse(responseManager.hasPendingResponse(requestId));
    }

    @Test
    void testClearAllResponses() {
        // 创建多个响应
        CompletableFuture<String> future1 = responseManager.createResponse("test-005", "test");
        CompletableFuture<String> future2 = responseManager.createResponse("test-006", "test");

        assertEquals(2, responseManager.getPendingResponseCount());

        // 清理所有响应
        responseManager.clearAllResponses();

        assertEquals(0, responseManager.getPendingResponseCount());
        assertTrue(future1.isDone());
        assertTrue(future2.isDone());
        assertTrue(future1.isCancelled());
        assertTrue(future2.isCancelled());
    }

    @Test
    void testCleanupTimeoutResponses() throws InterruptedException {
        // 创建响应
        responseManager.createResponse("test-007", "test");

        assertEquals(1, responseManager.getPendingResponseCount());

        // 等待一段时间
        Thread.sleep(100);

        // 清理超时响应（设置很短的超时时间）
        responseManager.cleanupTimeoutResponses(0); // 0分钟，立即清理

        assertEquals(0, responseManager.getPendingResponseCount());
    }
}
